### Check how booking looks when school books

### Next

- recheck transaction error issue occurrance. might have to change some flows
- check when .session is set in service-booking [done]
- accordingly set income-log session_status [done]
- verify for other mentoring types [partial_done_later]
- actual log creation on cancel, complete
- shift the logic for substitute
- handle for workshops
- check how to determine premium workshop

**IMP** Currently we create income for type: mentoring and serviceRoles: 'mentoring', 'substitute', 'consultant'
**-**In future we need to handle for correcting
**-** Even substitute has type: mentoring, then why: 'substitute' in ServiceType

### Need to handle

- Statistics update in order.hooks.ts create just like completeOrder
- attribute.type === ‘workshop’ checking is wrong, need to check specific session type

- expected_pending
- cancels
- actual_pending
