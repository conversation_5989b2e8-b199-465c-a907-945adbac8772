import {Application} from '../../../declarations'
import {startTransactionSession, commitTransactionSession, rollbackTransactionSession, TxnParams} from '../../../hooks/dbTransactions'
import {shouldExecute} from '../../../hooks/cronScheduler'

export class IncomeProcessor {
  constructor(private app: Application) {}

  async generateIncomeLog({order}: any) {
    console.log('generateIncomeLog', order._id, order.type)
    if (!order.price || order.price <= 0) {
      return
    }
    console.log('generateIncomeLog_1', order.type)

    const eligibleOrderTypes = ['unit', 'session_self_study', 'premium_cloud', 'prompt', 'session_public']
    if (!eligibleOrderTypes.includes(order.type)) {
      return
    }

    const styleCategoryMapping: {[key: string]: string} = {
      unit: 'library_content',
      premium_cloud: 'premium_contents_task_sessions',
      prompt: 'new_prompt_content',
    }

    // Process each link and collect eligible ones
    const eligibleLinks = []
    for (const link of order.links || []) {
      // const remainingPrice = Number((link.price - link.refundPrice).toFixed(0))
      // if (remainingPrice <= 0) {
      //   continue
      // }

      if (!link.goods?.uid) continue

      let isEligible = false
      let category = null

      if (['premium_cloud', 'unit', 'prompt'].includes(link.style)) {
        isEligible = true
        category = styleCategoryMapping[link.style]
      } else if (order.type === 'session_self_study' && link.style === 'session') {
        isEligible = true
        category = 'self_study_content'
      } else if (order.type === 'session_public' && link.style === 'session') {
        isEligible = true //Agl.paidWorkshopTypes.includes(link.goods.type)
        category = 'teaching_service'
      }

      if (isEligible && category) {
        eligibleLinks.push({
          link,
          category,
          setting_category: category,
        })
      }
    }

    if (eligibleLinks.length === 0) {
      await this.markOrderAsProcessed(order._id)
      return
    }
    console.log('eligibleLinks:', eligibleLinks.length)
    const session = await startTransactionSession(this.app)
    const transactionParams = {mongoose: {session}, sideEffectsToExecute: []}

    try {
      for (const eligibleLink of eligibleLinks) {
        switch (eligibleLink.category) {
          case 'teaching_service': {
            if (order.incomeStatus === 'expected_pending') await this.insertWorkhopExpIncomeLog(eligibleLink, order, transactionParams)
            // else if (order.incomeStatus === 'actual_pending') await this.insertWorkhopActIncomeLog(eligibleLink, order, transactionParams)
            break
          }
          default: {
            await this.insertCommonIncomeLog(eligibleLink, order, transactionParams)
          }
        }
      }

      const updatedStatus = order.incomeStatus === 'expected_pending' ? 'expected_processed' : 'actual_processed'

      await this.app.service('order').Model.updateOne({_id: order._id}, {$set: {incomeStatus: updatedStatus}}, {session})

      await commitTransactionSession(session, this.app, transactionParams)
      console.log(`Order ${order._id}: Income processing completed successfully`)
    } catch (error) {
      await rollbackTransactionSession(session)
      throw error
    }
  }

  async insertCommonIncomeLog(eligibleLink: {link: any; category: string; setting_category: string}, order: any, params: TxnParams) {
    const {link, category, setting_category} = eligibleLink
    const incomeLogData = {
      uid: link.goods.uid,
      isSchool: false,
      tab: 'earn',
      category,
      setting_category,
      status: 1,
      businessId: `${order._id.toString()}-${link.id.toString()}`,
      event_details: {
        id: link.id,
        name: link.name,
        cover: link.cover,
        style: link.style,
        orderId: order._id.toString(),
        orderType: order.type,
      },
      amount: link.price || 0,
    }

    const incomePromises = [this.app.service('income-log').createIncomeLog(incomeLogData, params)]

    if (link.style === 'premium_cloud' && link.goods.approval?.approver) {
      const auditorIncomeLogData = {
        ...incomeLogData,
        uid: link.goods.approval.approver,
        category: 'premium_content_audit',
        setting_category: 'premium_content_audit',
      }
      incomePromises.push(this.app.service('income-log').createIncomeLog(auditorIncomeLogData, params))
    }

    await Promise.all(incomePromises)
  }

  async insertWorkhopExpIncomeLog(eligibleLink: {link: any; category: string; setting_category: string}, order: any, params: TxnParams) {
    console.log('insertWorkhopExpIncomeLog', eligibleLink.category)
    const {link, category} = eligibleLink
    const {goods} = link
    // todo
    // recheck the logic for expected log creation
    if (!goods || !goods.uid) {
      console.log(`Skipping workshop income log - no goods or uid for link ${link.id}`)
      return
    }
    const childSessionIds = goods.childs?.length ? goods.childs.map((child: any) => child._id && child.sessionType === 'live').filter(Boolean) : []
    const setting_category = goods.uid === goods.task?.uid ? 'teaching_service_own' : 'teaching_service_classcipe'

    // Create parent income log for the main workshop
    const parentIncomeLogData = {
      uid: goods.uid,
      isSchool: false,
      tab: 'earn',
      category,
      setting_category,
      status: 0,
      isParent: childSessionIds.length > 0 ? true : undefined,
      businessId: `${goods._id.toString()}-${order.buyer}`,
      event_details: {
        id: goods._id.toString(),
        name: goods.name,
        cover: goods.image,
        orderId: order._id.toString(),
        orderType: order.type,
        sessionStatus: 'scheduled',
        sessionType: goods.type,
      },
      amount: link.price || 0,
    }

    // Insert primary income log
    console.log('parentIncomeLogData', JSON.stringify(parentIncomeLogData))
    const parentLog = await this.app.service('income-log').createIncomeLog(parentIncomeLogData, params)
    console.log('Created parent income log -', parentLog?._id)
    // If this is a parent type and has children, process child sessions
    if (parentLog?._id && childSessionIds.length > 0) {
      const childSessions = await this.app
        .service('session')
        .Model.find({
          _id: {$in: childSessionIds},
        })
        .lean()

      for (const childSession of childSessions) {
        const session = childSession as any

        console.log('childSession', childSession._id)

        const childIncomeLogData = {
          uid: goods.uid,
          isSchool: false,
          tab: 'earn',
          category,
          setting_category,
          status: 0,
          isParent: false,
          parentId: parentLog._id?.toString(),
          businessId: `${session._id.toString()}-${order.buyer}`,
          event_details: {
            id: session._id.toString(),
            name: session.name,
            cover: session.image,
            orderId: order._id.toString(),
            orderType: order.type,
            sessionStatus: 'scheduled',
            sessionType: session.type,
          },
          amount: link.price / childSessions.length, // todo_ask
        }

        await this.app.service('income-log').createIncomeLog(childIncomeLogData, params)
        console.log(`Created child session income log - session: ${session._id}`)
      }
    }
  }

  async processIncomeCron() {
    // Check if should execute (every 30 minutes = 48 times per day)
    // if (!shouldExecute(48)) {
    //   return
    // }

    try {
      const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000)

      const ordersToProcess = await this.app
        .service('order')
        .Model.find({
          incomeStatus: {$in: ['expected_pending', 'actual_pending']},
          // createdAt: {$lt: tenMinutesAgo},
        })
        .limit(100) // Process max 100 orders per run to avoid timeout
        .lean()

      console.log(`Income processing cron: Found ${ordersToProcess.length} orders to process`)

      for (const order of ordersToProcess) {
        try {
          await this.generateIncomeLog({order})
        } catch (error) {}
      }
    } catch (error) {}
  }

  private async markOrderAsProcessed(orderId: string) {
    try {
      await this.app.service('order').Model.updateOne({_id: orderId}, {$set: {incomeStatus: 'actual_processed'}})
    } catch (error) {}
  }
}

export const isIncomePending = (orderType: string, orderStatus: number, price: number) => {
  if (![200, 500, 501, 502].includes(orderStatus) || !price || price <= 0) {
    return null
  }
  let incomeStatus: string | null = null
  if (orderStatus === 200 && ['unit', 'session_self_study', 'premium_cloud', 'prompt'].includes(orderType)) {
    incomeStatus = 'actual_pending'
  } else if (['session_public'].includes(orderType)) {
    incomeStatus = orderStatus === 200 ? 'expected_pending' : 'actual_pending' // not exactly true for order cancellation
  }
  return incomeStatus
}

export async function insertWorkhopActIncomeLog(order: any, childSessions: any[], app: Application, params: TxnParams) {
  const notesMap: {[key: number]: string} = {
    500: ' Amount change due to user refund (cancelled by participant)',
    501: 'Amount change due to user refund (cancelled by facilitator)',
    502: 'Amount change due to user refund (cancelled by system)',
  }

  // Step 1: Check if order type is session_public, if not return
  if (order.type !== 'session_public') {
    return
  }

  // Step 2: Find session link with pending === true
  const sessionLink = order.links.find((item: any) => item.style === 'session' && item.pending === true)
  if (!sessionLink) {
    return
  }

  // Step 3: Check if childSessions are provided for selective cancellation
  if (childSessions && childSessions.length > 0) {
    // Handle selective child session cancellation
    await handleChildSessionCancellation(order, sessionLink, childSessions, notesMap, app, params)
  } else {
    // Handle full session cancellation
    await handleFullSessionCancellation(order, sessionLink, notesMap, app, params)
  }
}

// Helper function to update child session income logs
async function updateChildSessionLogs(childSessions: any[], link: any, order: any, notesMap: {[key: number]: string}, app: Application, params: TxnParams) {
  for (const childSession of childSessions) {
    await app.service('income-log').updateIncomeLog(
      {
        uid: link.goods.uid,
        businessId: `${childSession._id.toString()}-${order.buyer}`,
        category: 'teaching_service',
        status: 0,
      },
      {
        $set: {
          value: 0,
          status: 1,
          notes: notesMap[order.status],
          'event_details.sessionStatus': 'cancelled',
        },
      },
      params
    )
  }
}

// Helper function to calculate parent log value from child logs
async function calculateParentLogValue(liveSessions: any[], link: any, order: any, app: Application, params: TxnParams): Promise<number> {
  const options = Acan.getTxnOptions(params)
  const childLogs = await app
    .service('income-log')
    .Model.find(
      {
        uid: link.goods.uid,
        businessId: {$in: liveSessions.map((v: any) => `${v._id.toString()}-${order.buyer}`)},
        category: 'teaching_service',
      },
      null,
      options
    )
    .select('value')
    .lean()

  return childLogs.reduce((prev: any, cur: any) => prev + cur.value, 0)
}

// Helper function to update parent session income log
async function updateParentSessionLog(
  link: any,
  order: any,
  notesMap: {[key: number]: string},
  parentLogValue: number,
  sessionStatus: string,
  isFullyCancelled: boolean,
  app: Application,
  params: TxnParams
) {
  await app.service('income-log').updateIncomeLog(
    {
      uid: link.goods.uid,
      businessId: `${link.id.toString()}-${order.buyer}`,
      category: 'teaching_service',
      status: 0,
    },
    {
      $set: {
        value: parentLogValue,
        status: isFullyCancelled ? 1 : 0,
        notes: isFullyCancelled ? notesMap[order.status] : '',
        'event_details.sessionStatus': sessionStatus,
      },
    },
    params
  )
}

async function handleChildSessionCancellation(
  order: any,
  link: any,
  childSessions: any[],
  notesMap: {[key: number]: string},
  app: Application,
  params: TxnParams
) {
  // Update income logs for specific child sessions
  await updateChildSessionLogs(childSessions, link, order, notesMap, app, params)

  // Calculate parent log status and value
  const liveSessions = link.goods?.childs?.length ? link.goods.childs.filter((v: any) => v.sessionType === 'live') : []
  const totalRefundedItems = link.refundedItems ? link.refundedItems.concat(childSessions) : childSessions
  const isFullyCancelled = link.removed === true || totalRefundedItems.length >= liveSessions.length

  let parentLogValue = 0
  if (isFullyCancelled) {
    parentLogValue = await calculateParentLogValue(liveSessions, link, order, app, params)
  }

  const sessionStatus = isFullyCancelled ? (totalRefundedItems.length === liveSessions.length ? 'cancelled' : 'partially_cancelled') : 'ongoing'

  await updateParentSessionLog(link, order, notesMap, parentLogValue, sessionStatus, isFullyCancelled, app, params)
}

async function handleFullSessionCancellation(order: any, link: any, notesMap: {[key: number]: string}, app: Application, params: TxnParams) {
  // Check if link is fully removed (all child sessions cancelled)
  if (link.removed === true) {
    return
  }

  // Get live sessions
  const liveSessions = link.goods?.childs?.length ? link.goods.childs.filter((v: any) => v.sessionType === 'live') : []

  // Update all child session logs if they exist
  if (liveSessions.length > 0) {
    await updateChildSessionLogs(liveSessions, link, order, notesMap, app, params)
  }

  // Calculate parent log value and update parent log
  const parentLogValue = await calculateParentLogValue(liveSessions, link, order, app, params)
  await updateParentSessionLog(link, order, notesMap, parentLogValue, 'cancelled', true, app, params)
}
